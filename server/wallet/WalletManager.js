import { ethers } from 'ethers';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

/**
 * WalletManager handles secure wallet operations on the server side
 * This prevents private keys from being exposed in client-side code
 */
class WalletManager {
    constructor() {
        // Initialize provider and wallet
        this.provider = new ethers.JsonRpcProvider(process.env.ETHEREUM_NETWORK_URL || 'http://localhost:8545');

        // Initialize hot wallet from environment variables
        if (process.env.HOT_WALLET_PRIVATE_KEY && process.env.HOT_WALLET_ADDRESS) {
            this.hotWallet = new ethers.Wallet(process.env.HOT_WALLET_PRIVATE_KEY, this.provider);
            this.hotWalletAddress = process.env.HOT_WALLET_ADDRESS;

            // Initialize nonce management for concurrent transactions
            this.pendingNonce = null;
            this.noncePromise = null;

            console.log('🔐 Hot wallet initialized securely on server');
        } else {
            console.error('❌ Hot wallet configuration missing in environment variables');
            this.hotWallet = null;
            this.hotWalletAddress = null;
            // Initialize nonce management for concurrent transactions
            this.pendingNonce = null;
            this.noncePromise = null;
            this.nonceLock = false; // Lock for nonce requests
        }
    }

    /**
     * Reset nonce tracking (useful for testing)
     */
    resetNonceTracking() {
        this.pendingNonce = null;
        this.noncePromise = null;
        console.log('🔄 Nonce tracking reset');
    }

    /**
     * Get next nonce for hot wallet transactions (handles concurrency)
     * @returns {Promise<number>} Next available nonce
     */
    async getNextNonce() {
        if (!this.hotWallet) {
            throw new Error('Hot wallet not initialized');
        }

        // Use a proper mutex lock to prevent race conditions
        const release = await this.acquireNonceLock();
        
        try {
            // Get current nonce from network with retry logic
            let networkNonce;
            let retries = 3;
            while (retries > 0) {
                try {
                    networkNonce = await this.provider.getTransactionCount(this.hotWalletAddress, 'pending');
                    break;
                } catch (error) {
                    retries--;
                    if (retries === 0) throw error;
                    await new Promise(resolve => setTimeout(resolve, 100)); // Wait before retry
                }
            }

            // Reset our tracking if network nonce is ahead of our expected nonce
            if (this.pendingNonce !== null && networkNonce >= this.pendingNonce) {
                console.log(`🔄 Resetting nonce tracking: network=${networkNonce}, pending=${this.pendingNonce}`);
                this.pendingNonce = null;
            }

            // Use the higher of network nonce or our tracked pending nonce
            const nextNonce = this.pendingNonce !== null ? Math.max(networkNonce, this.pendingNonce) : networkNonce;

            // Update our pending nonce for the next transaction
            this.pendingNonce = nextNonce + 1;

            console.log(`🔢 Next nonce for hot wallet: ${nextNonce} (network: ${networkNonce}, pending: ${this.pendingNonce})`);
            return nextNonce;
        } finally {
            release(); // Always release the lock
        }
    }

    /**
     * Acquire a mutex lock for nonce management
     * @returns {Promise<Function>} Release function
     */
    async acquireNonceLock() {
        return new Promise((resolve) => {
            const tryAcquire = () => {
                if (!this.nonceLock) {
                    this.nonceLock = true;
                    resolve(() => { this.nonceLock = false; });
                } else {
                    setTimeout(tryAcquire, 10);
                }
            };
            tryAcquire();
        });
    }

    /**
     * Get hot wallet balance
     * @returns {Promise<string>} Balance in ETH
     */
    async getHotWalletBalance() {
        if (!this.hotWallet) {
            throw new Error('Hot wallet not initialized');
        }
        
        try {
            const balance = await this.provider.getBalance(this.hotWalletAddress);
            return ethers.formatEther(balance);
        } catch (error) {
            console.error('❌ Failed to get hot wallet balance:', error);
            throw new Error('Failed to get hot wallet balance');
        }
    }
    
    /**
     * Send ETH from hot wallet to a recipient
     * @param {string} toAddress - Recipient address
     * @param {string} amount - Amount in ETH
     * @param {string} reason - Reason for the transaction (for logging)
     * @returns {Promise<object>} Transaction result
     */
    async sendFromHotWallet(toAddress, amount, reason = 'unspecified') {
        if (!this.hotWallet) {
            throw new Error('Hot wallet not initialized');
        }
        
        try {
            console.log(`💰 Sending ${amount} ETH from hot wallet to ${toAddress} for ${reason}`);
            
            // Convert amount to wei
            const amountWei = ethers.parseEther(amount.toString());

            // Get current balance before transaction
            const balanceBefore = await this.provider.getBalance(this.hotWalletAddress);
            console.log(`💰 Hot wallet balance before transaction: ${ethers.formatEther(balanceBefore)} ETH`);

            // Get next nonce to prevent concurrent transaction conflicts
            const nonce = await this.getNextNonce();

            // Send transaction with retry logic and exponential backoff
            const tx = await this.sendTransactionWithRetry({
                to: toAddress,
                value: amountWei,
                nonce: nonce
            });
            
            console.log(`📤 Transaction sent: ${tx.hash}`);
            
            // Wait for transaction confirmation
            const receipt = await tx.wait();
            console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);
            
            // Get balance after transaction
            const balanceAfter = await this.provider.getBalance(this.hotWalletAddress);
            console.log(`💰 Hot wallet balance after transaction: ${ethers.formatEther(balanceAfter)} ETH`);
            
            return {
                success: true,
                transactionHash: tx.hash,
                blockNumber: receipt.blockNumber,
                from: this.hotWalletAddress,
                to: toAddress,
                amount: amount,
                reason: reason,
                gasUsed: receipt.gasUsed.toString(),
                balanceBefore: ethers.formatEther(balanceBefore),
                balanceAfter: ethers.formatEther(balanceAfter)
            };
        } catch (error) {
            console.error('❌ Failed to send transaction from hot wallet:', error);

            // If it's a nonce error, reset nonce tracking and retry once
            if (error.message.includes('nonce') || error.message.includes('Nonce')) {
                console.log('🔄 Nonce error detected, resetting nonce tracking and retrying...');
                this.resetNonceTracking();

                try {
                    // Retry with fresh nonce
                    const newNonce = await this.getNextNonce();
                    const retryTx = await this.hotWallet.sendTransaction({
                        to: toAddress,
                        value: ethers.parseEther(amount.toString()),
                        nonce: newNonce
                    });

                    const retryReceipt = await retryTx.wait();
                    const balanceAfter = await this.provider.getBalance(this.hotWalletAddress);

                    console.log(`✅ Retry successful: ${retryTx.hash}`);

                    return {
                        success: true,
                        transactionHash: retryTx.hash,
                        blockNumber: retryReceipt.blockNumber,
                        from: this.hotWalletAddress,
                        to: toAddress,
                        amount: amount,
                        reason: reason,
                        gasUsed: retryReceipt.gasUsed.toString(),
                        balanceBefore: 'N/A (retry)',
                        balanceAfter: ethers.formatEther(balanceAfter),
                        retried: true
                    };
                } catch (retryError) {
                    console.error('❌ Retry also failed:', retryError);
                    throw new Error(`Failed to send transaction after retry: ${retryError.message}`);
                }
            }

            throw new Error(`Failed to send transaction: ${error.message}`);
        }
    }
    
    /**
     * Send transaction with exponential backoff retry logic
     * @param {Object} txConfig - Transaction configuration
     * @returns {Promise<Object>} Transaction object
     */
    async sendTransactionWithRetry(txConfig, maxRetries = 3) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    // Exponential backoff: 100ms, 200ms, 400ms
                    const delay = Math.pow(2, attempt - 1) * 100;
                    console.log(`⏳ Retry attempt ${attempt}, waiting ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    // Get fresh nonce for retry
                    txConfig.nonce = await this.getNextNonce();
                }
                
                const tx = await this.hotWallet.sendTransaction(txConfig);
                console.log(`📤 Transaction sent: ${tx.hash}`);
                return tx;
                
            } catch (error) {
                lastError = error;
                
                // Check if it's a nonce-related error
                if (error.message.includes('nonce') || error.message.includes('Nonce')) {
                    console.log(`🔄 Nonce error on attempt ${attempt + 1}: ${error.message}`);
                    
                    if (attempt < maxRetries) {
                        // Reset nonce tracking and try again
                        this.resetNonceTracking();
                        continue;
                    }
                } else if (error.message.includes('replacement transaction underpriced')) {
                    console.log(`🔄 Transaction underpriced on attempt ${attempt + 1}`);
                    
                    if (attempt < maxRetries) {
                        // Increase gas price for replacement transaction
                        if (txConfig.gasPrice) {
                            txConfig.gasPrice = txConfig.gasPrice * 110n / 100n; // 10% increase
                        }
                        continue;
                    }
                } else {
                    // Non-retryable error
                    throw error;
                }
            }
        }
        
        throw new Error(`Transaction failed after ${maxRetries + 1} attempts: ${lastError.message}`);
    }

    /**
     * Validate Ethereum address
     * @param {string} address - Ethereum address to validate
     * @returns {boolean} True if valid
     */
    isValidAddress(address) {
        try {
            return ethers.isAddress(address);
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Get hot wallet address (public information)
     * @returns {string|null} Hot wallet address
     */
    getHotWalletAddress() {
        return this.hotWalletAddress;
    }
    
    /**
     * Check if wallet is properly initialized
     * @returns {boolean} True if wallet is ready
     */
    isWalletReady() {
        return this.hotWallet !== null && this.hotWalletAddress !== null;
    }

    /**
     * Get balance of an address
     * @param {string} address - Ethereum address to check balance for
     * @returns {Promise<string>} Balance in ETH
     */
    async getBalance(address) {
        try {
            const balance = await this.provider.getBalance(address);
            return ethers.formatEther(balance);
        } catch (error) {
            console.error(`❌ Failed to get balance for ${address}:`, error);
            throw new Error(`Failed to get balance: ${error.message}`);
        }
    }
}

// Export singleton instance
export default new WalletManager();