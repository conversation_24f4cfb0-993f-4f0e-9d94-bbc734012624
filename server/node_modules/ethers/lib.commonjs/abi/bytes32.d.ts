/**
 *  About bytes32 strings...
 *
 *  @_docloc: api/utils:Bytes32 Strings
 */
import type { BytesLike } from "../utils/index.js";
/**
 *  Encodes %%text%% as a Bytes32 string.
 */
export declare function encodeBytes32String(text: string): string;
/**
 *  Encodes the Bytes32-encoded %%bytes%% into a string.
 */
export declare function decodeBytes32String(_bytes: BytesLike): string;
//# sourceMappingURL=bytes32.d.ts.map