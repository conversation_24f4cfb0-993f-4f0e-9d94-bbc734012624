{"version": 3, "file": "ethers.d.ts", "sourceRoot": "", "sources": ["../src.ts/ethers.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,OAAO,EACH,mBAAmB,EAAE,mBAAmB,EAExC,QAAQ,EACR,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAEzI,iBAAiB,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,sBAAsB,EACvG,KAAK,GACR,MAAM,gBAAgB,CAAC;AAExB,OAAO,EACH,UAAU,EAAE,cAAc,EAC1B,gBAAgB,EAAE,iBAAiB,EACnC,aAAa,EAAE,SAAS,EAAE,cAAc,EAC3C,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EACH,WAAW,EACX,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAChD,QAAQ,EACR,WAAW,EAAE,aAAa,EAC7B,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EACH,YAAY,EAAE,QAAQ,EACtB,eAAe,EACf,oBAAoB,EAAE,0BAA0B,EAAE,2BAA2B,EAAE,2BAA2B,EAAE,QAAQ,EAAE,iBAAiB,EAC1I,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EACH,WAAW,EACX,WAAW,EACX,SAAS,EACT,SAAS,EACT,MAAM,EAAE,MAAM,EACd,MAAM,EACN,MAAM,EAAE,UAAU,EAClB,IAAI,EACJ,SAAS,EAAE,UAAU,EACxB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EACH,EAAE,EACF,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAC9C,iBAAiB,EAAE,mBAAmB,EACtC,WAAW,EAAE,aAAa,EAC1B,cAAc,EAAE,uBAAuB,EAAE,oBAAoB,EAC7D,gBAAgB,EAChB,eAAe,EAClB,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EACH,kBAAkB,EAElB,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,mBAAmB,EAE5D,cAAc,EAAE,YAAY,EAAE,UAAU,EAExC,gBAAgB,EAEhB,gBAAgB,EAChB,kBAAkB,EAAE,eAAe,EAAE,aAAa,EAElD,eAAe,EAEf,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EACrE,kBAAkB,EAAE,iBAAiB,EAAE,cAAc,EACrD,uBAAuB,EAAE,cAAc,EAAE,iBAAiB,EAE1D,iBAAiB,EAAE,cAAc,EAAE,iBAAiB,EAEpD,WAAW,EACX,OAAO,EAEP,SAAS,EAAE,eAAe,EAC1B,oBAAoB,EAAE,4BAA4B,EAClD,aAAa,EAAE,aAAa,EAAE,uBAAuB,EAErD,qBAAqB,EAAE,qBAAqB,EAAE,uBAAuB,EACrE,gBAAgB,EAAE,mBAAmB,EAErC,WAAW,EAAE,mBAAmB,EACnC,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EACH,aAAa,EAAE,gBAAgB,EAC/B,cAAc,EAAE,cAAc,EAC9B,WAAW,EACd,MAAM,wBAAwB,CAAC;AAEhC,OAAO,EACH,YAAY,EAAE,YAAY,EAC1B,YAAY,EAAE,YAAY,EAC1B,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAC9D,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EACpE,gBAAgB,EAAE,iBAAiB,EACnC,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,eAAe,EAAE,aAAa,EAC3E,SAAS,EACT,eAAe,EAAE,OAAO,EACxB,YAAY,EACZ,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAC9C,WAAW,EACX,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EACjF,QAAQ,EAAE,MAAM,EAAE,IAAI,EACtB,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAChD,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAC3C,cAAc,EACd,SAAS,EAAE,SAAS,EACpB,MAAM,GACT,MAAM,kBAAkB,CAAC;AAE1B,OAAO,EACH,QAAQ,EACR,UAAU,EAAE,YAAY,EAAE,gBAAgB,EAC1C,MAAM,EAEN,WAAW,EAEX,cAAc,EAAE,qBAAqB,EACrC,eAAe,EAAE,cAAc,EAE/B,oBAAoB,EAAE,uBAAuB,EAAE,mBAAmB,EAClE,mBAAmB,EAAE,uBAAuB,GAC/C,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EACH,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EACzD,MAAM,sBAAsB,CAAC;AAO9B,YAAY,EACR,YAAY,EAAE,gBAAgB,EAC9B,UAAU,EAAE,YAAY,EACxB,YAAY,EACZ,iBAAiB,EAAE,sBAAsB,EAC5C,MAAM,gBAAgB,CAAC;AAExB,YAAY,EACR,WAAW,EAAE,WAAW,EAAE,YAAY,EACzC,MAAM,oBAAoB,CAAC;AAE5B,YAAY,EACR,sBAAsB,EAAE,aAAa,EAAE,iBAAiB,EAAE,iBAAiB,EAC3E,iBAAiB,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAC1E,mBAAmB,EAAE,SAAS,EAC9B,kBAAkB,EAAE,yBAAyB,EAAE,gBAAgB,EAC/D,eAAe,EAClB,MAAM,qBAAqB,CAAC;AAE7B,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAEzE,YAAY,EACR,oBAAoB,EAAE,eAAe,EAAE,cAAc,EACxD,MAAM,iBAAiB,CAAC;AAEzB,YAAY,EACR,QAAQ,EAAE,MAAM,EAEhB,oBAAoB,EAEpB,uBAAuB,EAAE,sBAAsB,EAAE,uBAAuB,EAExE,sBAAsB,EAAE,WAAW,EAAE,QAAQ,EAAE,sBAAsB,EACrE,cAAc,EAAE,yBAAyB,EAAE,eAAe,EAC1D,mBAAmB,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAC3D,iBAAiB,EAAE,yBAAyB,EAAE,YAAY,EAC1D,cAAc,EAAE,aAAa,EAAE,yBAAyB,EAAE,SAAS,EACnE,UAAU,EAAE,wBAAwB,EAAE,UAAU,EAAE,YAAY,EAC9D,mBAAmB,EAAE,oBAAoB,EAAE,wBAAwB,EACnE,0BAA0B,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EACnE,WAAW,EAAE,wBAAwB,EAAE,kBAAkB,EACzD,yBAAyB,EAAE,gBAAgB,EAAE,aAAa,EAC7D,MAAM,sBAAsB,CAAC;AAE9B,YAAY,EACR,UAAU,EAAE,aAAa,EAAE,eAAe,EAC1C,aAAa,EAAE,iBAAiB,EAChC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAC1C,eAAe,EAClB,MAAM,wBAAwB,CAAC;AAEhC,YAAY,EACR,SAAS,EACT,YAAY,EAAE,OAAO,EACrB,SAAS,EACT,WAAW,EACX,aAAa,EAAE,wBAAwB,EAAE,eAAe,EACxD,iBAAiB,EAAE,oBAAoB,EAEvC,cAAc,EACd,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EACpD,gBAAgB,EAAE,eAAe,EAEjC,WAAW,EAAE,YAAY,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,YAAY,EACvF,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,EAC3E,iBAAiB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,uBAAuB,EACtF,kBAAkB,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,kBAAkB,EACjF,2BAA2B,EAAE,wBAAwB,EAAE,qBAAqB,EAC5E,mBAAmB,EACnB,gBAAgB,EAEhB,mBAAmB,EAAE,wBAAwB,EAC7C,gBAAgB,EAAE,QAAQ,EAC7B,MAAM,kBAAkB,CAAC;AAE1B,YAAY,EACR,gBAAgB,EAAE,eAAe,EAAE,cAAc,EACpD,MAAM,mBAAmB,CAAC"}