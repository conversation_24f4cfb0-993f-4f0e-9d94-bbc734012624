{"version": 3, "file": "provider-browser.js", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-browser.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9D,OAAO,EAAE,yBAAyB,EAAE,MAAM,uBAAuB,CAAC;AAmBjE,CAAC;AAmFF;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,yBAAyB;IAC1D,QAAQ,CAA6E;IAErF,aAAa,CAA6B;IAE1C;;;OAGG;IACH,YAAY,QAAyB,EAAE,OAAoB,EAAE,QAAiC;QAE1F,mBAAmB;QACnB,MAAM,OAAO,GAA8B,MAAM,CAAC,MAAM,CAAC,EAAG,EAC1D,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA,CAAC,CAAC,EAAG,CAAC,EACpC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;QAExB,cAAc,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE,2BAA2B,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEhG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE;YACnC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC;SAC9C;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,MAAc,EAAE,MAAwC,EAAE,EAAE;YAC/E,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,OAAO,EAAE,CAAC,CAAC;YAC9D,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC/D,OAAO,MAAM,CAAC;aACjB;YAAC,OAAO,CAAM,EAAE;gBACb,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC7B,KAAM,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAM,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAM,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,qBAAqB,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC7D,MAAM,KAAK,CAAC;aACf;QACL,CAAC,CAAC;IACN,CAAC;IAED,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,MAAwC;QAC/D,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QAEpB,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,OAA+C;QACvD,cAAc,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,yCAAyC,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAEvG,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAG,CAAC,CAAC;YAC1E,OAAO,CAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,CAAE,CAAC;SACzC;QAAC,OAAO,CAAM,EAAE;YACb,OAAO,CAAE;oBACL,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE;iBAC5D,CAAE,CAAC;SACP;IACL,CAAC;IAED,WAAW,CAAC,OAAuB,EAAE,KAAmB;QAEpD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAE1C,kEAAkE;QAClE,oCAAoC;QACpC,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;YAC5B,KAAK,IAAI;gBACL,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,uBAAwB,KAAK,CAAC,KAAK,CAAC,OAAQ,EAAE,CAAC;gBACrE,MAAM;YACV,KAAK,IAAI;gBACL,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,uBAAwB,KAAK,CAAC,KAAK,CAAC,OAAQ,EAAE,CAAC;gBACrE,MAAM;SACb;QAED,OAAO,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,OAAwB;QACpC,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC,CAAC;SAAE;QAErC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAG,CAAC,CAAC;QACtD,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC9B,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;SACtC;QAED,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAChC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IACtF,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAyB;QACrC,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC,CAAC;SAAE;QAErC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE;YAClC,IAAI;gBACA,MAAM,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAAG,CAAC,CAAC;aAEnD;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;aAC9D;SACJ;QAED,OAAO,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAgC;QAClD,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,EAAG,CAAC;SAAE;QAEvC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAChD;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAA,CAAC;YAC5C,CAAC,OAAM,CAAC,MAAM,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,IAAI,CAAC;QAEpD,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAErC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ,EAAE;YACjC,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,CAAC,kBAAkB,IAAI,OAAO,IAAI,eAAe,IAAI,OAAO;eAC5D,qBAAqB,IAAI,OAAO,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA,CAAC,CAAC,GAAG,CAAC;QACvD,IAAI,OAAO,KAAK,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAEnC,OAAO,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1C,IAAI,KAAK,GAAiC,EAAG,CAAC;YAE9C,MAAM,WAAW,GAAG,CAAC,KAA0B,EAAE,EAAE;gBAC/C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACzB,IAAI,WAAW,EAAE;oBAAE,QAAQ,EAAE,CAAC;iBAAE;YACpC,CAAC,CAAC;YAEF,MAAM,QAAQ,GAAG,GAAG,EAAE;gBAClB,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEpB,IAAI,KAAK,CAAC,MAAM,EAAE;oBAEd,4BAA4B;oBAC5B,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;wBAE3B,qDAAqD;wBACrD,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC5C,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEjC,IAAI,QAAQ,IAAI,IAAI,EAAE;4BAClB,uBAAuB;4BACvB,OAAO,CAAC,IAAI,CAAC,CAAC;yBAEjB;6BAAM,IAAI,QAAQ,YAAY,eAAe,EAAE;4BAC5C,0BAA0B;4BAC1B,OAAO,CAAC,QAAQ,CAAC,CAAC;yBAErB;6BAAM;4BACH,6BAA6B;4BAC7B,IAAI,KAAK,GAAiC,IAAI,CAAC;4BAC/C,IAAI,QAAQ,CAAC,IAAI,EAAE;gCACf,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC/B,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gCACnC,+CAA+C;gCAC/C,4BAA4B;gCAC5B,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;6BACtB;4BAED,IAAI,KAAK,EAAE;gCACP,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;gCACjC,OAAO,CAAC,IAAI,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE;oCAC7C,YAAY,EAAE,IAAI;iCACrB,CAAC,CAAC,CAAC;6BACP;iCAAM;gCACH,MAAM,CAAC,SAAS,CAAC,8BAA8B,EAAE,uBAAuB,EAAE;oCACtE,KAAK,EAAE,QAAQ;iCAClB,CAAC,CAAC,CAAC;6BACP;yBACJ;qBAEJ;yBAAM;wBAEH,gCAAgC;wBAChC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACpC,OAAO,CAAC,IAAI,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE;4BAC7C,YAAY,EAAE,IAAI;yBACrB,CAAC,CAAC,CAAC;qBACP;iBAEJ;qBAAM;oBACH,gBAAgB;oBAChB,OAAO,CAAC,IAAI,CAAC,CAAC;iBACjB;gBAED,OAAO,CAAC,mBAAmB,CAAM,0BAA0B,EACzD,WAAW,CAAC,CAAC;YACnB,CAAC,CAAC;YAEF,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAEzD,OAAO,CAAC,gBAAgB,CAAM,0BAA0B,EACtD,WAAW,CAAC,CAAC;YAEf,OAAO,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;CACJ"}