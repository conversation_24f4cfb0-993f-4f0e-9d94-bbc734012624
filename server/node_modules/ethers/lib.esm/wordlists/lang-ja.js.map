{"version": 3, "file": "lang-ja.js", "sourceRoot": "", "sources": ["../../src.ts/wordlists/lang-ja.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,EAAE,EAAE,MAAM,kBAAkB,CAAC;AACtC,OAAO,EACH,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EACrD,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAGzC,MAAM,IAAI,GAAG;IAET,eAAe;IACf,orEAAorE;IAEprE,eAAe;IACf,ssGAAssG;IAEtsG,eAAe;IACf,4uDAA4uD;IAE5uD,eAAe;IACf,olBAAolB;IAEplB,eAAe;IACf,4JAA4J;IAE5J,eAAe;IACf,0GAA0G;IAE1G,gBAAgB;IAChB,WAAW;CACd,CAAC;AAEF,sDAAsD;AACtD,MAAM,OAAO,GAAG,6FAA6F,CAAA;AAE7G,IAAI,SAAS,GAAyB,IAAI,CAAC;AAE3C,SAAS,GAAG,CAAC,IAAY;IACrB,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,MAAM,GAAG,sBAAsB,CAAC;AACtC,MAAM,KAAK,GAAG,sBAAsB,CAAA;AAEpC,SAAS,QAAQ,CAAC,IAAmB;IACjC,OAAO,YAAY,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,SAAS;IACd,IAAI,SAAS,KAAK,IAAI,EAAE;QAAE,OAAO,SAAS,CAAC;KAAE;IAE7C,MAAM,QAAQ,GAAkB,EAAE,CAAC;IAEnC,yDAAyD;IACzD,MAAM,SAAS,GAAwC,EAAE,CAAC;IAE1D,6BAA6B;IAC7B,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC7C,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAE7C,yDAAyD;IACzD,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjE,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjE,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjE,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAGjE,sCAAsC;IACtC,SAAS,SAAS,CAAC,IAAY;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,MAAM,KAAK,KAAK,EAAE;gBAAE,SAAS;aAAE;YACnC,IAAI,MAAM,EAAE;gBAAE,IAAI,GAAW,MAAM,CAAC;aAAE;YACtC,MAAM,IAAI,IAAI,CAAC;SAClB;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,uCAAuC;IACvC,SAAS,YAAY,CAAC,CAAS,EAAE,CAAS;QACtC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,CAAC;SAAE;QACzB,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC;SAAE;QACxB,OAAO,CAAC,CAAC;IACb,CAAC;IAED,qBAAqB;IACrB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE;QACxC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3B,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE;YACtD,MAAM,IAAI,GAAkB,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAChC;YACD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;SACjC;KACJ;IACD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAE5B,6DAA6D;IAC7D,uBAAuB;IACvB,YAAY;IACZ,aAAa;IAEb,wDAAwD;IACxD,qBAAqB;IACrB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;QAC/D,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACvB;IACD,oBAAoB;IAEpB,qDAAqD;IACrD,wBAAwB;IACxB,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChD,qBAAqB;IACrB,IAAI,QAAQ,KAAK,oEAAoE,EAAE;QACnF,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC9D;IACD,oBAAoB;IAEpB,SAAS,GAAG,QAAQ,CAAC;IAErB,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED,IAAI,QAAQ,GAAkB,IAAI,CAAC;AAEnC;;;;GAIG;AACH,MAAM,OAAO,MAAO,SAAQ,QAAQ;IAEhC;;;;;;;OAOG;IACH,gBAAgB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE9B,OAAO,CAAC,KAAa;QACjB,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC;QAC1B,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,EAC7C,uBAAwB,KAAM,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,YAAY,CAAC,IAAY;QACrB,OAAO,SAAS,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,MAAc;QAChB,2BAA2B;QAC3B,OAAO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,CAAC,KAAoB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,QAAQ;QACX,IAAI,QAAQ,IAAI,IAAI,EAAE;YAAE,QAAQ,GAAG,IAAI,MAAM,EAAE,CAAC;SAAE;QAClD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ"}