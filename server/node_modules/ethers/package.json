{"author": "<PERSON> <<EMAIL>>", "browser": {"./lib.esm/crypto/crypto.js": "./lib.esm/crypto/crypto-browser.js", "./lib.esm/providers/provider-ipcsocket.js": "./lib.esm/providers/provider-ipcsocket-browser.js", "./lib.esm/providers/ws.js": "./lib.esm/providers/ws-browser.js", "./lib.esm/utils/base64.js": "./lib.esm/utils/base64-browser.js", "./lib.esm/utils/geturl.js": "./lib.esm/utils/geturl-browser.js", "./lib.esm/wordlists/wordlists.js": "./lib.esm/wordlists/wordlists-browser.js"}, "dependencies": {"@adraffy/ens-normalize": "1.10.1", "@noble/curves": "1.2.0", "@noble/hashes": "1.3.2", "@types/node": "22.7.5", "aes-js": "4.0.0-beta.5", "tslib": "2.7.0", "ws": "8.17.1"}, "description": "A complete and compact Ethereum library, for dapps, wallets and any other tools.", "devDependencies": {"@rollup/plugin-node-resolve": "15.3.0", "@types/mocha": "10.0.9", "@types/semver": "7.5.8", "c8": "7.12.0", "mocha": "10.7.3", "rollup": "4.24.0", "semver": "7.6.3", "typescript": "5.0.4", "uglify-js": "3.19.3"}, "engines": {"node": ">=14.0.0"}, "ethereum": "donations.ethers.eth", "exports": {".": {"import": "./lib.esm/index.js", "default": "./lib.commonjs/index.js"}, "./abi": {"import": "./lib.esm/abi/index.js", "default": "./lib.commonjs/abi/index.js"}, "./address": {"import": "./lib.esm/address/index.js", "default": "./lib.commonjs/address/index.js"}, "./constants": {"import": "./lib.esm/constants/index.js", "default": "./lib.commonjs/constants/index.js"}, "./contract": {"import": "./lib.esm/contract/index.js", "default": "./lib.commonjs/contract/index.js"}, "./crypto": {"import": "./lib.esm/crypto/index.js", "default": "./lib.commonjs/crypto/index.js"}, "./hash": {"import": "./lib.esm/hash/index.js", "default": "./lib.commonjs/hash/index.js"}, "./providers": {"import": "./lib.esm/providers/index.js", "default": "./lib.commonjs/providers/index.js"}, "./transaction": {"import": "./lib.esm/transaction/index.js", "default": "./lib.commonjs/transaction/index.js"}, "./utils": {"import": "./lib.esm/utils/index.js", "default": "./lib.commonjs/utils/index.js"}, "./wallet": {"import": "./lib.esm/wallet/index.js", "default": "./lib.commonjs/wallet/index.js"}, "./wordlists": {"import": "./lib.esm/wordlists/index.js", "default": "./lib.commonjs/wordlists/index.js"}}, "funding": [{"type": "individual", "url": "https://github.com/sponsors/ethers-io/"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "9944ec94b154b4f8fdfeefb81a1e47b28fd907bc", "homepage": "https://ethers.org", "keywords": ["ethereum", "ethers", "et<PERSON><PERSON>s"], "license": "MIT", "main": "./lib.commonjs/index.js", "module": "./lib.esm/index.js", "name": "ethers", "publishConfig": {"access": "public", "tag": "latest"}, "repository": {"type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"_build-dist": "rollup -c && uglifyjs ./dist/ethers.js -o ./dist/ethers.min.js && uglifyjs ./dist/ethers.umd.js -o ./dist/ethers.umd.min.js && uglifyjs ./dist/wordlists-extra.js -o ./dist/wordlists-extra.min.js", "_dist-stats": "gzip -k9f -S '.gz' ./dist/ethers.min.js && gzip -k9f -S '.gz' ./dist/ethers.umd.min.js && gzip -k9f -S '.gz' ./dist/wordlists-extra.min.js && du -hs ./dist/*.gz && echo '' && du -hs ./dist/*.js", "auto-build": "npm run build -- -w", "build": "tsc --project tsconfig.esm.json", "build-all": "npm run build && npm run build-commonjs", "build-clean": "npm run clean && npm run build && node lib.esm/_admin/update-version.js && node lib.esm/_admin/update-changelog.js && npm run build-all && npm run _build-dist && npm run _dist-stats", "build-commonjs": "tsc --project tsconfig.commonjs.json", "build-dist": "npm run build && npm run _build-dist && npm run _dist-stats", "clean": "rm -rf dist lib.esm lib.commonjs && cp -r misc/basedirs/* .", "postpublish": "node lib.esm/_admin/create-release.js", "stats": "echo 'Dependencies' && npm ls --all --omit=dev", "test": "npm run test-esm", "test-browser": "node lib.esm/_admin/test-browser", "test-commonjs": "mocha --reporter ./reporter.cjs ./lib.commonjs/_tests/test-*.js", "test-coverage": "c8 -o output -r lcov -r text mocha --no-color --reporter ./reporter.cjs ./lib.esm/_tests/test-*.js | tee output/summary.txt", "test-esm": "mocha --trace-warnings --reporter ./reporter.cjs ./lib.esm/_tests/test-*.js"}, "sideEffects": false, "version": "6.15.0"}