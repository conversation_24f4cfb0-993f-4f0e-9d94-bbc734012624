⚡ grinder_3 starting maximum stress behavior
🎮 Level 1: 1148 WISH → ETH (let game calculate)
🎁 Awarding 114.80000000000001 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1148 WISH → 114.80000000000001 ETH)
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
🎮 Level 1: 1000 WISH → ETH (let game calculate)
🎁 Awarding 100 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1000 WISH → 100 ETH)
⚡ whale_1 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 0
✅ API Call: POST /wallet/send (154ms)
✅ REAL ETH transfer completed: 108.2 ETH to ******************************************
📤 Transaction hash: 0x1619ee4e83a4325f2c3b007bb5a31eb9048a04a0148c29696544d1f286476d00
✅ API Call: POST /generate-environment (156ms)
🎨 Environment Creation Tracked: Desert oasis with Environment by creator_1
✅ Environment created by creator_1: Desert oasis with Environment
✅ Environment created: Desert oasis with Environment by creator_1
✅ Environment created: Desert oasis with Environment by creator_1
🎨 creator_2 creating environment: "Cyberpunk neon cityscape with rain"
💸 creator_2 spending 2500 ETH for: Reality Warp: Cyberpunk neon cityscape with rain
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Cyberpunk neon cityscape with rain
🔢 Next nonce for creator_2: 0
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
🎮 Level 1: 1171 WISH → ETH (let game calculate)
🎁 Awarding 117.10000000000001 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1171 WISH → 117.10000000000001 ETH)
✅ API Call: POST /wallet/send (181ms)
✅ REAL ETH transfer completed: 114.80000000000001 ETH to ******************************************
📤 Transaction hash: 0xc4bcbe41e41aa9fc4090f2e9d9f6db35bb22bb986d37fc01e4c328f73bee7e2f
✅ REAL blockchain transaction mined: 0x7a4c66cb8e56f1715969c500dcd28f5d6a4ee09cc60cd8db5bd62caf44322190
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 335.0614935230834ms before next transaction...
⚡ whale_2 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for whale_2: 0
✅ REAL blockchain transaction mined: 0xbaadd156d08bc3e6cd7363b245f3c8df2eef005f01004488162ecc2c1d646080
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 342.43051916769014ms before next transaction...
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
🎮 Level 1: 1201 WISH → ETH (let game calculate)
🎁 Awarding 120.10000000000001 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1201 WISH → 120.10000000000001 ETH)
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Ice planet with aurora borealis"
💸 creator_1 spending 2500 ETH for: Reality Warp: Ice planet with aurora borealis
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Ice planet with aurora borealis
🔢 Next nonce for creator_1: 1
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Volcanic landscape with lava flows"
💸 creator_2 spending 2500 ETH for: Reality Warp: Volcanic landscape with lava flows
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Volcanic landscape with lava flows
✅ API Call: POST /generate-environment (113ms)
🎨 Environment Creation Tracked: Cyberpunk neon cityscape Environment by creator_2
✅ Environment created by creator_2: Cyberpunk neon cityscape Environment
✅ Environment created: Cyberpunk neon cityscape Environment by creator_2
✅ Environment created: Cyberpunk neon cityscape Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_1 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🔢 Next nonce for creator_2: 1
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
✅ REAL blockchain transaction mined: 0x7806b830c14b82586788c1a6dba9c010f30f011ae6a7e626fcf9939270397fef
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for whale_1: 1
🎮 Level 2: 1174 WISH → ETH (let game calculate)
🎁 Awarding 117.4 ETH to grinder_1 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1174 WISH → 117.4 ETH)
⚡ casual_1 starting maximum stress behavior
⚠️ whale_1: Attempt 1 failed for http://localhost:3001/api/environments/env_1_1756654556342/purchase, retrying in 2277.278457665265ms
⚠️ grinder_1: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2481.9407395323733ms
⚠️ grinder_1: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2324.3780650984822ms
⚠️ grinder_2: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2116.0638583174846ms
⚠️ grinder_3: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2519.2731368936056ms
✅ REAL blockchain transaction mined: 0xe2e68df4c95a35f2ec83dc98fb0a1078e5b2a4d02967b8397982fe6fff42ea08
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 2
✅ REAL blockchain transaction mined: 0xf743b895ad6d64acf381c008910ac674f991a3acdc44c64872920353134e7071
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 366.71549321975976ms before next transaction...
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🎮 Level 2: 1500 WISH → ETH (let game calculate)
🎁 Awarding 150 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1500 WISH → 150 ETH)
🔢 Next nonce for whale_2: 1
✅ REAL blockchain transaction mined: 0x6c7e292cf2a8897565820af72c26a19b015123247ec89ee1c51633bcf4487e41
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 527.4750565484052ms before next transaction...
⚡ casual_2 starting maximum stress behavior
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
✅ API Call: POST /generate-environment (120ms)
🎨 Environment Creation Tracked: Ice planet with Environment by creator_1
✅ Environment created by creator_1: Ice planet with Environment
✅ Environment created: Ice planet with Environment by creator_1
✅ REAL blockchain transaction mined: 0x8c02f12cae037990a9b1f8400f625df1b39e08650362dbec604f86673b437abe
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 375.25063896905044ms before next transaction...
🔢 Next nonce for whale_2: 2
🎮 Level 2: 1067 WISH → ETH (let game calculate)
🎁 Awarding 106.7 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1067 WISH → 106.7 ETH)
✅ API Call: POST /generate-environment (117ms)
🎨 Environment Creation Tracked: Volcanic landscape with Environment by creator_2
✅ Environment created by creator_2: Volcanic landscape with Environment
✅ Environment created: Volcanic landscape with Environment by creator_2
⚡ casual_3 starting maximum stress behavior
⚠️ grinder_1: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2593.785099341395ms
✅ REAL blockchain transaction mined: 0x80ac08aeca5357f7ea7d0f8d1daa34d1ed1f0b00362bcf7c771c8ca141e41c3d
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 548.3454813278355ms before next transaction...
⚠️ grinder_2: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2612.8054257157773ms
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 3
⚠️ grinder_3: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2864.534047637853ms
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_2: 3
✅ REAL blockchain transaction mined: 0xb858ff72093f336796193e7a98e88dfaaa2516d16f7888a54d6ceb65ac75d355
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 521.0691744630925ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
✅ REAL blockchain transaction mined: 0x257c6c856e3a8ed3730cf6ebdf139fd840affff69589ee1d527fec15a60eb1dc
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 505.87999707495965ms before next transaction...
🔢 Next nonce for whale_1: 4
✅ REAL blockchain transaction mined: 0x2bac24bf52794d09c87cbf7fb8e39dd294cd30463d905c43ab6ba9a24f6e0fd8
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 730.5909687977665ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 4
✅ REAL blockchain transaction mined: 0x84e2c5aeea24dcd00b9e1b6721904cfe8cdd2f0f7a84d47ff8969255e55b6a0a
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 770.9685678099606ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 5
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 5
✅ REAL blockchain transaction mined: 0x85e44f1783d06a78cc84ea1fb5540d685ff4327bc460d6813eb6a08e0cd6ae2b
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 781.0290718717899ms before next transaction...
✅ REAL blockchain transaction mined: 0x7dc6b096240e3f7eab240ed5dce916890c7b48af20e7fcfbc00567006fa927e0
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 736.3666791402882ms before next transaction...
🎁 Awarding 37.5 ETH to whale_2 for: Level 1 completion - minimal effort (ETH test mode: 375 WISH → 37.5 ETH)
🎁 Awarding 37.5 ETH to whale_1 for: Level 1 completion - minimal effort (ETH test mode: 375 WISH → 37.5 ETH)
✅ API Call: POST /wallet/send (266ms)
✅ REAL ETH transfer completed: 37.5 ETH to ******************************************
📤 Transaction hash: 0xaf630734ae8f54f0f79e3226e15d601016de86f28b71e8f92ddbee2b01f4f622
✅ API Call: POST /wallet/send (238ms)
✅ REAL ETH transfer completed: 37.5 ETH to ******************************************
📤 Transaction hash: 0xa52f4f0de323f2559ab71652e5bb7149592e32be9457d20db040b31b2fa5fd08
⚠️ grinder_2: Attempt 2 failed for http://localhost:3001/api/wallet/send, retrying in 4627.735891287645ms
⚠️ whale_1: Attempt 2 failed for http://localhost:3001/api/environments/env_1_1756654556342/purchase, retrying in 4230.564886685111ms
⚠️ grinder_1: Attempt 2 failed for http://localhost:3001/api/wallet/send, retrying in 4053.8480982608057ms
⚠️ grinder_1: Attempt 2 failed for http://localhost:3001/api/wallet/send, retrying in 4678.577376285074ms
⚠️ grinder_3: Attempt 2 failed for http://localhost:3001/api/wallet/send, retrying in 4075.234615820601ms
🎁 Awarding 75 ETH to whale_2 for: Level 2 completion - minimal effort (ETH test mode: 750 WISH → 75 ETH)
🎁 Awarding 75 ETH to whale_1 for: Level 2 completion - minimal effort (ETH test mode: 750 WISH → 75 ETH)
✅ API Call: POST /wallet/send (3406ms)
✅ REAL ETH transfer completed: 117.4 ETH to ******************************************
📤 Transaction hash: 0xacc2ea9b3fc7a1cce3c343b95a7787d7264aba95c5e0bd4d14e49db6adc84d78
✅ API Call: POST /wallet/send (3399ms)
✅ REAL ETH transfer completed: 150 ETH to ******************************************
📤 Transaction hash: 0xc8ea0da0548bfd1d5a07920f1fd0868b745c90a38d3565911f1e8766a2770b8a
✅ API Call: POST /wallet/send (473ms)
✅ REAL ETH transfer completed: 75 ETH to ******************************************
📤 Transaction hash: 0x53a954c14f074e490017816c75038347ae3c5de549eb6367dd354e8414f62916
✅ API Call: POST /wallet/send (473ms)
✅ REAL ETH transfer completed: 75 ETH to ******************************************
📤 Transaction hash: 0xc72cf6d3c641668a34ddd5c3252049a9dfbcfe8250031dd31e939d15553808db
✅ API Call: POST /wallet/send (3647ms)
✅ REAL ETH transfer completed: 106.7 ETH to ******************************************
📤 Transaction hash: 0x9993588e647da9b4bad47ff637da331150e9ae48012a1d02f7db87338100319b
🎮 Level 3: 1137 WISH → ETH (let game calculate)
🎁 Awarding 113.7 ETH to grinder_1 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1137 WISH → 113.7 ETH)
🎮 Level 3: 1150 WISH → ETH (let game calculate)
🎁 Awarding 115 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1150 WISH → 115 ETH)
💰 Balance change: +21616.499644 ETH (121616.499644 ETH total)
📈 Treasury inflow detected: +21616.499644 ETH
✅ API Call: POST /wallet/send (396ms)
✅ REAL ETH transfer completed: 113.7 ETH to ******************************************
📤 Transaction hash: 0xa73e43f63de776003c53beda8a1bd88e3cecc8ec3e6ee9ab30fb3840289f7088
🎮 Level 3: 1208 WISH → ETH (let game calculate)
🎁 Awarding 120.80000000000001 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1208 WISH → 120.80000000000001 ETH)
✅ API Call: POST /wallet/send (431ms)
✅ REAL ETH transfer completed: 115 ETH to ******************************************
📤 Transaction hash: 0xdeb4a9a116026f0bd37684b117b81e546980a687388ea34c38463e844444d65a
✅ API Call: POST /wallet/send (400ms)
✅ REAL ETH transfer completed: 120.80000000000001 ETH to ******************************************
📤 Transaction hash: 0x6de7bfa849f682ebb6f58cbd130060cf6ce5e57253491e1c84ef30c2de119002
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🎮 Level 4: 1176 WISH → ETH (let game calculate)
🎁 Awarding 117.60000000000001 ETH to grinder_1 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1176 WISH → 117.60000000000001 ETH)
🎮 Level 4: 1214 WISH → ETH (let game calculate)
🎁 Awarding 121.4 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1214 WISH → 121.4 ETH)
✅ API Call: POST /wallet/send (395ms)
✅ REAL ETH transfer completed: 117.60000000000001 ETH to ******************************************
📤 Transaction hash: 0xa5b8de87c34326cc6f6f5a739024e96a58dee9d73e550c65f0a3bdfe33fd6af2
🎮 Level 4: 1204 WISH → ETH (let game calculate)
🎁 Awarding 120.4 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1204 WISH → 120.4 ETH)
✅ API Call: POST /wallet/send (391ms)
✅ REAL ETH transfer completed: 121.4 ETH to ******************************************
📤 Transaction hash: 0x8a52ec75aaf93f58bddbd57f1bdf933b470ae7fce52c75495c3f800b28abc6eb
✅ API Call: POST /wallet/send (395ms)
✅ REAL ETH transfer completed: 120.4 ETH to ******************************************
📤 Transaction hash: 0xe3bafd3648d650fa02179edb607c4925c5acda64202bfd8789866c0062d49568
🎮 Level 5: 1200 WISH → ETH (let game calculate)
🎁 Awarding 120 ETH to grinder_1 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1200 WISH → 120 ETH)
🎮 Level 5: 1182 WISH → ETH (let game calculate)
🎁 Awarding 118.2 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1182 WISH → 118.2 ETH)
✅ API Call: POST /wallet/send (379ms)
✅ REAL ETH transfer completed: 120 ETH to ******************************************
📤 Transaction hash: 0xc5b07bd380818adacc5180fdd583ad122f43caf1003fc86cffe628320010f4bb
🎮 Level 5: 1200 WISH → ETH (let game calculate)
🎁 Awarding 120 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1200 WISH → 120 ETH)
✅ API Call: POST /wallet/send (359ms)
✅ REAL ETH transfer completed: 118.2 ETH to ******************************************
📤 Transaction hash: 0xd1cc3e85803188ae0b5ba427e212a8dab926dafedd77d4b47929128923a08e49
✅ API Call: POST /wallet/send (388ms)
✅ REAL ETH transfer completed: 120 ETH to ******************************************
📤 Transaction hash: 0x17a79b80762cc8f0a07cb2f2ca93d298f5a2b5f3fce487e97ce36faa5ba73036
🎮 Level 6: 1654 WISH → ETH (let game calculate)
🎁 Awarding 165.4 ETH to grinder_1 for: Level 6 completion - 100% enemies defeated (ETH test mode: 1654 WISH → 165.4 ETH)
🎮 Level 6: 1774 WISH → ETH (let game calculate)
🎁 Awarding 177.4 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated (ETH test mode: 1774 WISH → 177.4 ETH)
⚠️ whale_1: Attempt 3 failed for http://localhost:3001/api/environments/env_1_1756654556342/purchase, retrying in 8569.912737874134ms
⚠️ grinder_1: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2615.8098455651934ms
⚠️ grinder_1: Attempt 3 failed for http://localhost:3001/api/wallet/send, retrying in 8737.353526294659ms
⚠️ grinder_2: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2955.9764665833272ms
🎮 Level 6: 1667 WISH → ETH (let game calculate)
🎁 Awarding 166.70000000000002 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated (ETH test mode: 1667 WISH → 166.70000000000002 ETH)
⚠️ grinder_3: Attempt 3 failed for http://localhost:3001/api/wallet/send, retrying in 8177.518553764638ms
⚠️ grinder_2: Attempt 3 failed for http://localhost:3001/api/wallet/send, retrying in 8188.159534149289ms
⚠️ grinder_3: Attempt 1 failed for http://localhost:3001/api/wallet/send, retrying in 2838.696969234615ms
⚠️ grinder_1: Attempt 3 failed for http://localhost:3001/api/wallet/send, retrying in 8426.169222000677ms
💰 Balance change: -953.400170 ETH (120663.099474 ETH total)
📉 Treasury outflow detected: -953.400170 ETH
✅ API Call: POST /wallet/send (3194ms)
✅ REAL ETH transfer completed: 165.4 ETH to ******************************************
📤 Transaction hash: 0x7bfdf3e6db6fa113630d0a548b43a9f3536adc7d43d0d24e2df68edea9711a0f
✅ API Call: POST /wallet/send (3405ms)
✅ REAL ETH transfer completed: 177.4 ETH to ******************************************
📤 Transaction hash: 0xb9132decad2fcab155e4af1975df8a77aca4b60e42723278b14b5ee44f4bc742
🎮 Level 7: 1845 WISH → ETH (let game calculate)
🎁 Awarding 184.5 ETH to grinder_1 for: Level 7 completion - 100% enemies defeated (ETH test mode: 1845 WISH → 184.5 ETH)
✅ API Call: POST /wallet/send (3283ms)
✅ REAL ETH transfer completed: 166.70000000000002 ETH to ******************************************
📤 Transaction hash: 0x214a133c6b801cd13ca2eeb0bbce514d989f0707143f86dd63361edb9d63fd25
✅ API Call: POST /wallet/send (175ms)
✅ REAL ETH transfer completed: 184.5 ETH to ******************************************
📤 Transaction hash: 0xe3541119516c4d09630c762bde17a9380d2ed603df5cc1673e136687a692c59d
🎮 Level 7: 1600 WISH → ETH (let game calculate)
🎁 Awarding 160 ETH to grinder_2 for: Level 7 completion - 100% enemies defeated (ETH test mode: 1600 WISH → 160 ETH)
✅ API Call: POST /wallet/send (158ms)
✅ REAL ETH transfer completed: 160 ETH to ******************************************
📤 Transaction hash: 0xea74063af978cb24e69563133e661559f1a030b3e707becfa86f8ba3c8b3517f
🎮 Level 7: 1791 WISH → ETH (let game calculate)
🎁 Awarding 179.10000000000002 ETH to grinder_3 for: Level 7 completion - 100% enemies defeated (ETH test mode: 1791 WISH → 179.10000000000002 ETH)
✅ API Call: POST /wallet/send (138ms)
✅ REAL ETH transfer completed: 179.10000000000002 ETH to ******************************************
📤 Transaction hash: 0x59549e3e61233b3e4e2d8c83c695000ba6d7f17bda924325386ee5bebb1ec45d
🎮 Level 8: 1600 WISH → ETH (let game calculate)
🎁 Awarding 160 ETH to grinder_1 for: Level 8 completion - 100% enemies defeated (ETH test mode: 1600 WISH → 160 ETH)
✅ API Call: POST /wallet/send (159ms)
✅ REAL ETH transfer completed: 160 ETH to ******************************************
📤 Transaction hash: 0xf8e5a802fdf675f2c837ad906ae598411d5d1a5b6f812493d91ae9765d1f9c28
🎮 Level 8: 1722 WISH → ETH (let game calculate)
🎁 Awarding 172.20000000000002 ETH to grinder_2 for: Level 8 completion - 100% enemies defeated (ETH test mode: 1722 WISH → 172.20000000000002 ETH)
✅ API Call: POST /wallet/send (156ms)
✅ REAL ETH transfer completed: 172.20000000000002 ETH to ******************************************
📤 Transaction hash: 0x8075013481aea3f0444e4da4ceb243b03b4880c8be02d76cda42325bdcc841bb
🎮 Level 8: 1865 WISH → ETH (let game calculate)
🎁 Awarding 186.5 ETH to grinder_3 for: Level 8 completion - 100% enemies defeated (ETH test mode: 1865 WISH → 186.5 ETH)
✅ API Call: POST /wallet/send (149ms)
✅ REAL ETH transfer completed: 186.5 ETH to ******************************************
📤 Transaction hash: 0xeb46d291fa29cae5cb4449ba7196e76d5c3abc4c84180eb265b741c9d794a755
🎮 Level 9: 1667 WISH → ETH (let game calculate)
🎁 Awarding 166.70000000000002 ETH to grinder_1 for: Level 9 completion - 100% enemies defeated (ETH test mode: 1667 WISH → 166.70000000000002 ETH)
✅ API Call: POST /wallet/send (155ms)
✅ REAL ETH transfer completed: 166.70000000000002 ETH to ******************************************
📤 Transaction hash: 0xf1ab1e794f7d00143e4fc725fbfbac651f44b4e22dd23d8e3b44768d41385406
🎮 Level 9: 2400 WISH → ETH (let game calculate)
🎁 Awarding 240 ETH to grinder_2 for: Level 9 completion - 100% enemies defeated (ETH test mode: 2400 WISH → 240 ETH)
✅ API Call: POST /wallet/send (157ms)
✅ REAL ETH transfer completed: 240 ETH to ******************************************
📤 Transaction hash: 0x5296bc3084404aa11cb16b90b2f1254e43ab88ceb98e62dc4cfe178a0417f385
🎮 Level 9: 1801 WISH → ETH (let game calculate)
🎁 Awarding 180.10000000000002 ETH to grinder_3 for: Level 9 completion - 100% enemies defeated (ETH test mode: 1801 WISH → 180.10000000000002 ETH)
✅ API Call: POST /wallet/send (140ms)
✅ REAL ETH transfer completed: 180.10000000000002 ETH to ******************************************
📤 Transaction hash: 0xbcc5f404f6ed6c23d856bb36276e922a82d4fa97625564386ba969488977a166
🎮 Level 10: 1750 WISH → ETH (let game calculate)
🎁 Awarding 175 ETH to grinder_1 for: Level 10 completion - 100% enemies defeated (ETH test mode: 1750 WISH → 175 ETH)
✅ API Call: POST /wallet/send (156ms)
✅ REAL ETH transfer completed: 175 ETH to ******************************************
📤 Transaction hash: 0x15cc06ac90429f426d499db56de3ec707f6deb8faa93ee7fbfa6a207252531b9
🎮 Level 10: 1600 WISH → ETH (let game calculate)
🎁 Awarding 160 ETH to grinder_2 for: Level 10 completion - 100% enemies defeated (ETH test mode: 1600 WISH → 160 ETH)
✅ API Call: POST /wallet/send (156ms)
✅ REAL ETH transfer completed: 160 ETH to ******************************************
📤 Transaction hash: 0x1cdcf846c0a03eb6e67b500c4382b238c6557f7b3402bed33fb85bac95d5b6ac
🎮 Level 10: 1920 WISH → ETH (let game calculate)
🎁 Awarding 192 ETH to grinder_3 for: Level 10 completion - 100% enemies defeated (ETH test mode: 1920 WISH → 192 ETH)
✅ API Call: POST /wallet/send (144ms)
✅ REAL ETH transfer completed: 192 ETH to ******************************************
📤 Transaction hash: 0x7770fd98dd9a270e5380be79515868b578ef40420e71a77f542faf87e087d089
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: -17997.499158 ETH
👤 Running casual player session: casual_1
🎁 Awarding 84.30000000000001 ETH to casual_1 for: Level 1 completion (ETH test mode: 843 WISH → 84.30000000000001 ETH)
✅ API Call: POST /wallet/send (154ms)
✅ REAL ETH transfer completed: 84.30000000000001 ETH to ******************************************
📤 Transaction hash: 0xc19bc1f82749ff6c0026d3e5fc4bc19f97e0fc2a95080c238a764de8fa850e5c
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🎁 Awarding 168.70000000000002 ETH to casual_1 for: Level 2 completion (ETH test mode: 1687 WISH → 168.70000000000002 ETH)
✅ API Call: POST /wallet/send (157ms)
✅ REAL ETH transfer completed: 168.70000000000002 ETH to ******************************************
📤 Transaction hash: 0x310b6641692186cb4e5e0f68263657701e113ac629343af7f5feb8ab4a86cf17
💰 Balance change: -2918.600358 ETH (117744.499116 ETH total)
📉 Treasury outflow detected: -2918.600358 ETH
🎁 Awarding 253.10000000000002 ETH to casual_1 for: Level 3 completion (ETH test mode: 2531 WISH → 253.10000000000002 ETH)
✅ API Call: POST /wallet/send (152ms)
✅ REAL ETH transfer completed: 253.10000000000002 ETH to ******************************************
📤 Transaction hash: 0xf261c874b13316648e3cc09dd34d75b16bfd3954da8c70f48869e714741fb271
❌ Mystical environment purchase failed for whale_1: Error: Mystical environment purchase API failed: 429
    at UserSimulator.simulateMysticalEnvironmentPurchases (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:397:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:444:13)
    at async Promise.all (index 1)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:307:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ Stress test failed: Error: Mystical environment purchase API failed: 429
    at UserSimulator.simulateMysticalEnvironmentPurchases (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:397:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:444:13)
    at async Promise.all (index 1)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:307:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ Stress test failed: Error: Mystical environment purchase API failed: 429
    at UserSimulator.simulateMysticalEnvironmentPurchases (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/UserSimulator.js:397:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TokenomicsStressTest.runCreatorRewardTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:444:13)
    at async Promise.all (index 1)
    at async TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:307:13)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)